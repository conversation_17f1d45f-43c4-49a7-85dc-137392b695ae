import React, { useRef, useEffect, useState } from 'react';
import * as PIXI from 'pixi.js';
import { Monitor, Smartphone, Volume2, VolumeX } from 'lucide-react';
import { useLoadingJourneyStore } from './LoadingJourneyStore';

interface ProfessionalLoadingPreviewProps {
  loadingProgress: number;
  currentPhase: number;
  assetCategories: Array<{
    name: string;
    loaded: number;
    total: number;
    status: string;
  }>;
  isLoading: boolean;
  className?: string;
}

// Industry standard slot resolutions
const SLOT_RESOLUTIONS = {
  desktop: {
    width: 1600,
    height: 900,
    aspectRatio: 16 / 9,
    label: '1600×900 (16:9)'
  },
  mobile_portrait: {
    width: 720,
    height: 1280,
    aspectRatio: 9 / 16,
    label: '720×1280 (9:16)'
  },
  mobile_landscape: {
    width: 1280,
    height: 720,
    aspectRatio: 16 / 9,
    label: '1280×720 (16:9)'
  }
};

const ProfessionalLoadingPreview: React.FC<ProfessionalLoadingPreviewProps> = ({
  loadingProgress,
  currentPhase,
  assetCategories,
  isLoading,
  className = ''
}) => {
  const pixiContainerRef = useRef<HTMLDivElement>(null);
  const pixiAppRef = useRef<PIXI.Application | null>(null);

  // Get loading config from store instead of props
  const journeyStore = useLoadingJourneyStore();
  const loadingConfig = journeyStore.config.loadingExperience;

  // Use store for device mode and orientation
  const [viewMode, setViewMode] = useState<'desktop' | 'mobile' | 'mobile-landscape'>('desktop');
  const [isMuted, setIsMuted] = useState(false);

  // Get current resolution based on view mode
  const getCurrentResolution = () => {
    if (viewMode === 'desktop') {
      return SLOT_RESOLUTIONS.desktop;
    }
    return viewMode === 'mobile-landscape'
      ? SLOT_RESOLUTIONS.mobile_landscape
      : SLOT_RESOLUTIONS.mobile_portrait;
  };

  const currentRes = getCurrentResolution();

  useEffect(() => {
    if (!pixiContainerRef.current) return;

    // Get the full container dimensions
    const containerRect = pixiContainerRef.current.getBoundingClientRect();
    const containerWidth = containerRect.width || 800;
    const containerHeight = containerRect.height || 600;

    // Initialize PIXI Application to fill the entire container
    const app = new PIXI.Application({
      width: containerWidth,
      height: containerHeight,
      backgroundColor: parseInt(loadingConfig.backgroundColor.replace('#', ''), 16),
      antialias: true,
      resolution: window.devicePixelRatio || 1,
      autoDensity: true,
    });

    pixiAppRef.current = app;
    // Make canvas fill the entire container
    const canvas = app.view as HTMLCanvasElement;
    canvas.style.width = '100%';
    canvas.style.height = '100%';
    pixiContainerRef.current.appendChild(canvas);

    // Create loading screen elements
    setupLoadingScreen(app);

    return () => {
      if (pixiAppRef.current) {
        pixiAppRef.current.destroy(true, {
          children: true,
          texture: true,
          baseTexture: true
        });
        pixiAppRef.current = null;
      }
    };
  }, [viewMode, loadingConfig.backgroundColor]);

  const setupLoadingScreen = (app: PIXI.Application) => {
    // Clear existing stage
    app.stage.removeChildren();

    // Background
    const background = new PIXI.Graphics();
    background.beginFill(parseInt(loadingConfig.backgroundColor.replace('#', ''), 16));
    background.drawRect(0, 0, app.view.width, app.view.height);
    background.endFill();
    app.stage.addChild(background);

    // Studio Logo
    if (loadingConfig.studioLogo) {
      addStudioLogo(app);
    }

    // Progress Bar
    addProgressBar(app);

    // Loading Text
    addLoadingText(app);
  };

  const addStudioLogo = async (app: PIXI.Application) => {
    if (!loadingConfig.studioLogo) return;

    try {
      const texture = await PIXI.Texture.fromURL(loadingConfig.studioLogo);
      const sprite = new PIXI.Sprite(texture);
      
      // Scale logo proportionally
      const logoScale = loadingConfig.studioLogoSize / Math.max(texture.width, texture.height);
      sprite.scale.set(logoScale);
      
      // Position based on percentage
      sprite.x = (app.view.width * loadingConfig.studioLogoPosition.x / 100) - sprite.width / 2;
      sprite.y = (app.view.height * loadingConfig.studioLogoPosition.y / 100) - sprite.height / 2;
      
      app.stage.addChild(sprite);
    } catch (error) {
      console.warn('Failed to load studio logo texture:', error);
    }
  };

  const addProgressBar = (app: PIXI.Application) => {
    if (loadingConfig.progressStyle === 'circular') {
      addCircularProgress(app);
    } else {
      addBarProgress(app);
    }

    // Add loading sprite for all positions except 'in-bar' (which is handled within progress functions)
    if (loadingConfig.loadingSprite && loadingConfig.spritePosition !== 'in-bar') {
      addLoadingSpriteForPosition(app);
    }
  };

  const addBarProgress = (app: PIXI.Application) => {
    const barWidth = app.view.width * (loadingConfig.progressBarWidth / 100);
    const barHeight = 8;
    const barX = (app.view.width * loadingConfig.progressBarPosition.x / 100) - (barWidth / 2);
    const barY = app.view.height * loadingConfig.progressBarPosition.y / 100;

    // Progress bar background
    const progressBg = new PIXI.Graphics();
    progressBg.beginFill(0xffffff, 0.2);
    progressBg.drawRoundedRect(barX, barY, barWidth, barHeight, barHeight / 2);
    progressBg.endFill();
    app.stage.addChild(progressBg);

    // Progress bar fill - always fill based on actual progress percentage
    const progressFill = new PIXI.Graphics();
    progressFill.beginFill(parseInt(loadingConfig.accentColor.replace('#', ''), 16));

    // Progress bar always fills to the actual progress percentage
    const progressFillWidth = barWidth * (loadingProgress / 100);
    progressFill.drawRoundedRect(barX, barY, progressFillWidth, barHeight, barHeight / 2);

    progressFill.endFill();
    app.stage.addChild(progressFill);

    // Loading sprite (if configured for progress indicator)
    if (loadingConfig.loadingSprite && loadingConfig.spritePosition === 'in-bar') {
      addLoadingSprite(app, barX, barY, barWidth);
    }
  };

  const addCircularProgress = (app: PIXI.Application) => {
    const centerX = app.view.width * loadingConfig.progressBarPosition.x / 100;
    const centerY = app.view.height * loadingConfig.progressBarPosition.y / 100;
    const radius = 50; // Fixed radius for circular progress
    const strokeWidth = 8;

    // Background circle
    const backgroundCircle = new PIXI.Graphics();
    backgroundCircle.lineStyle(strokeWidth, 0xffffff, 0.2);
    backgroundCircle.drawCircle(centerX, centerY, radius);
    app.stage.addChild(backgroundCircle);

    // Progress arc
    const progressArc = new PIXI.Graphics();
    const progressAngle = (loadingProgress / 100) * Math.PI * 2;

    if (loadingProgress > 0) {
      progressArc.lineStyle(strokeWidth, parseInt(loadingConfig.accentColor.replace('#', ''), 16));
      progressArc.arc(centerX, centerY, radius, -Math.PI / 2, -Math.PI / 2 + progressAngle);
    }

    app.stage.addChild(progressArc);

    // Loading sprite for circular progress (positioned at the end of the arc)
    if (loadingConfig.loadingSprite && loadingConfig.spritePosition === 'in-bar') {
      const spriteAngle = -Math.PI / 2 + progressAngle;
      const spriteX = centerX + Math.cos(spriteAngle) * radius;
      const spriteY = centerY + Math.sin(spriteAngle) * radius;
      addLoadingSpriteAtPosition(app, spriteX, spriteY);
    }
  };

  const addLoadingSprite = async (app: PIXI.Application, barX: number, barY: number, barWidth: number) => {
    if (!loadingConfig.loadingSprite) return;

    try {
      const texture = await PIXI.Texture.fromURL(loadingConfig.loadingSprite);
      const sprite = new PIXI.Sprite(texture);

      // Scale sprite
      const spriteScale = loadingConfig.spriteSize / Math.max(texture.width, texture.height);
      sprite.scale.set(spriteScale);
      sprite.anchor.set(0.5);

      // Position sprite center exactly at the END of the progress fill
      const progressEndX = barX + (barWidth * loadingProgress / 100);
      sprite.x = progressEndX;

      // Position sprite directly ON TOP of the progress bar (covering it)
      sprite.y = barY + (8 / 2); // Position at the center of the 8px tall progress bar

      // Add enhanced animations
      addSpriteAnimation(app, sprite);

      app.stage.addChild(sprite);
    } catch (error) {
      console.warn('Failed to load sprite texture:', error);
    }
  };

  const addLoadingSpriteAtPosition = async (app: PIXI.Application, x: number, y: number) => {
    if (!loadingConfig.loadingSprite) return;

    try {
      const texture = await PIXI.Texture.fromURL(loadingConfig.loadingSprite);
      const sprite = new PIXI.Sprite(texture);

      // Scale sprite
      const spriteScale = loadingConfig.spriteSize / Math.max(texture.width, texture.height);
      sprite.scale.set(spriteScale);
      sprite.anchor.set(0.5);

      // Position sprite at specified coordinates
      sprite.x = x;
      sprite.y = y;

      // Add enhanced animations
      addSpriteAnimation(app, sprite);

      app.stage.addChild(sprite);
    } catch (error) {
      console.warn('Failed to load sprite texture:', error);
    }
  };

  const addSpriteAnimation = (app: PIXI.Application, sprite: PIXI.Sprite) => {
    // Store initial values for animations
    const initialScale = sprite.scale.x;
    const initialX = sprite.x;
    const initialY = sprite.y;
    let animationTime = 0;

    // Create animation ticker function
    const animationTicker = () => {
      animationTime += 0.016; // Approximate 60fps delta time

      switch (loadingConfig.spriteAnimation) {
        case 'spin':
          sprite.rotation += 0.08; // Smooth rotation
          break;

        case 'roll':
          sprite.rotation += 0.12; // Faster rotation for rolling effect
          break;

        case 'bounce':
          // Smooth bounce using sine wave
          const bounceOffset = Math.sin(animationTime * 4) * 5;
          sprite.y = initialY + bounceOffset;
          break;

        case 'pulse':
          // Smooth pulsing scale using sine wave
          const pulseScale = initialScale + Math.sin(animationTime * 3) * 0.2;
          sprite.scale.set(pulseScale);
          break;

        case 'slide':
          // Gentle side-to-side sliding
          const slideOffset = Math.sin(animationTime * 2) * 3;
          sprite.x = initialX + slideOffset;
          break;
      }
    };

    // Add ticker to app
    app.ticker.add(animationTicker);
  };

  const addLoadingSpriteForPosition = async (app: PIXI.Application) => {
    if (!loadingConfig.loadingSprite) return;

    let spriteX = 0;
    let spriteY = 0;

    if (loadingConfig.progressStyle === 'circular') {
      // For circular progress, position relative to the circle
      const centerX = app.view.width * loadingConfig.progressBarPosition.x / 100;
      const centerY = app.view.height * loadingConfig.progressBarPosition.y / 100;
      const radius = 50;

      switch (loadingConfig.spritePosition) {
        case 'above-bar':
          spriteX = centerX;
          spriteY = centerY - radius - 40; // 40px above the circle
          break;
        case 'below-bar':
          spriteX = centerX;
          spriteY = centerY + radius + 40; // 40px below the circle
          break;
        case 'left-side':
          spriteX = centerX - radius - 60; // 60px to the left of circle
          spriteY = centerY;
          break;
        case 'right-side':
          spriteX = centerX + radius + 60; // 60px to the right of circle
          spriteY = centerY;
          break;
        default:
          return; // Don't add sprite for 'in-bar' or unknown positions
      }
    } else {
      // For bar progress, position relative to the progress bar
      const barWidth = app.view.width * (loadingConfig.progressBarWidth / 100);
      const barHeight = 8;
      const barX = (app.view.width * loadingConfig.progressBarPosition.x / 100) - (barWidth / 2);
      const barY = app.view.height * loadingConfig.progressBarPosition.y / 100;
      const barCenterX = app.view.width * loadingConfig.progressBarPosition.x / 100;

      switch (loadingConfig.spritePosition) {
        case 'above-bar':
          spriteX = barCenterX;
          spriteY = barY - 40; // 40px above the progress bar
          break;
        case 'below-bar':
          spriteX = barCenterX;
          spriteY = barY + 40; // 40px below the progress bar
          break;
        case 'left-side':
          spriteX = barX - 60; // 60px to the left of progress bar
          spriteY = barY + (barHeight / 2); // Vertically centered with bar
          break;
        case 'right-side':
          spriteX = barX + barWidth + 60; // 60px to the right of progress bar
          spriteY = barY + (barHeight / 2); // Vertically centered with bar
          break;
        default:
          return; // Don't add sprite for 'in-bar' or unknown positions
      }
    }

    // Use the existing addLoadingSpriteAtPosition function
    addLoadingSpriteAtPosition(app, spriteX, spriteY);
  };

  const addLoadingText = (app: PIXI.Application) => {
    // Only show percentage if enabled
    if (loadingConfig.showPercentage) {
      const percentStyle = new PIXI.TextStyle({
        fontFamily: 'Arial, sans-serif',
        fontSize: 18,
        fontWeight: 'bold',
        fill: loadingConfig.textColor,
        align: 'center'
      });

      const percent = new PIXI.Text(`${Math.round(loadingProgress)}%`, percentStyle);
      percent.anchor.set(0.5);

      // Position percentage based on progress style and user preference
      if (loadingConfig.progressStyle === 'circular') {
        // For circular progress, position relative to the circle center
        const centerX = app.view.width * loadingConfig.progressBarPosition.x / 100;
        const centerY = app.view.height * loadingConfig.progressBarPosition.y / 100;

        switch (loadingConfig.percentagePosition) {
          case 'above':
            percent.x = centerX;
            percent.y = centerY - 80; // 80px above the circle
            break;
          case 'below':
            percent.x = centerX;
            percent.y = centerY + 80; // 80px below the circle
            break;
          case 'right':
            percent.x = centerX + 80; // 80px to the right of circle
            percent.y = centerY;
            break;
          case 'center':
            percent.x = centerX;
            percent.y = centerY; // Exactly in the center of the circle
            break;
        }
      } else {
        // For bar progress, position relative to the progress bar
        const barWidth = app.view.width * (loadingConfig.progressBarWidth / 100);
        const barX = (app.view.width * loadingConfig.progressBarPosition.x / 100) - (barWidth / 2);
        const barY = app.view.height * loadingConfig.progressBarPosition.y / 100;

        // Calculate the center of the progress bar for proper alignment
        const barCenterX = app.view.width * loadingConfig.progressBarPosition.x / 100;

        switch (loadingConfig.percentagePosition) {
          case 'above':
            percent.x = barCenterX; // Center aligned with progress bar
            percent.y = barY - 30; // 30px above the progress bar
            break;
          case 'below':
            percent.x = barCenterX; // Center aligned with progress bar
            percent.y = barY + 30; // 30px below the progress bar
            break;
          case 'right':
            // Calculate safe zone based on sprite size to avoid overlap
            const spriteRadius = loadingConfig.loadingSprite ? loadingConfig.spriteSize / 2 : 0;
            const safeZonePadding = spriteRadius + 20; // Sprite radius + 20px padding
            percent.x = barX + barWidth + safeZonePadding;
            percent.y = barY; // Same vertical level as the progress bar
            break;
        }
      }

      app.stage.addChild(percent);
    }

    // Custom message (studio branding, etc.)
    if (loadingConfig.customMessage && loadingConfig.customMessage.trim()) {
      const messageStyle = new PIXI.TextStyle({
        fontFamily: 'Arial, sans-serif',
        fontSize: loadingConfig.customMessageSize,
        fill: loadingConfig.textColor,
        align: 'center'
      });

      const messageText = new PIXI.Text(loadingConfig.customMessage, messageStyle);
      messageText.anchor.set(0.5);
      messageText.x = app.view.width * loadingConfig.customMessagePosition.x / 100;
      messageText.y = app.view.height * loadingConfig.customMessagePosition.y / 100;

      app.stage.addChild(messageText);
    }
  };

  // Update PIXI elements when props change
  useEffect(() => {
    if (pixiAppRef.current) {
      setupLoadingScreen(pixiAppRef.current);
    }
  }, [loadingProgress, currentPhase, loadingConfig]);

  return (
    <div className={`w-full h-full flex flex-col bg-gray-100 rounded-lg shadow-lg border border-gray-200 ${className}`}>

      {/* Header with title - Exact same styling as PixiPreviewWrapper */}
      <div className="bg-gray-900 px-4 py-3 border-b border-gray-700 flex items-center justify-between rounded-t-lg">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
          <span className="text-gray-300 text-sm font-medium">Loading Experience Preview (PixiJS)</span>
        </div>
        <div className="flex items-center gap-4">
          <div className="text-gray-500 text-xs">
            {currentRes.label} • {viewMode === 'desktop' ? 'Desktop' : viewMode === 'mobile' ? 'Mobile Portrait' : 'Mobile Landscape'} mode
          </div>
          <div className="flex items-center gap-1 bg-gray-800 rounded-lg p-1">
            <button
              onClick={() => setViewMode('desktop')}
              className={`flex items-center gap-1 px-2 py-1 rounded text-xs transition-all ${
                viewMode === 'desktop'
                  ? 'bg-blue-600 text-white shadow-sm'
                  : 'text-gray-400 hover:text-gray-200 hover:bg-gray-700'
              }`}
            >
              <Monitor className="w-3 h-3" />
            </button>
            <button
              onClick={() => setViewMode('mobile')}
              className={`flex items-center gap-1 px-2 py-1 rounded text-xs transition-all ${
                viewMode === 'mobile'
                  ? 'bg-blue-600 text-white shadow-sm'
                  : 'text-gray-400 hover:text-gray-200 hover:bg-gray-700'
              }`}
            >
              <Smartphone className="w-3 h-3" />
            </button>
            <button
              onClick={() => setViewMode('mobile-landscape')}
              className={`flex items-center gap-1 px-2 py-1 rounded text-xs transition-all ${
                viewMode === 'mobile-landscape'
                  ? 'bg-blue-600 text-white shadow-sm'
                  : 'text-gray-400 hover:text-gray-200 hover:bg-gray-700'
              }`}
            >
              <Smartphone className="w-3 h-3 rotate-90" />
            </button>
          </div>
          <button
            onClick={() => setIsMuted(!isMuted)}
            className={`p-1 rounded transition-all ${
              isMuted
                ? 'text-gray-500 hover:text-gray-300'
                : 'text-blue-400 hover:text-blue-300'
            }`}
          >
            {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
          </button>
        </div>
      </div>

      {/* Game preview area with PixiJS loading screen */}
      <div className="flex-1 relative bg-black overflow-auto flex flex-col rounded-b-lg">
        {/* Device/Browser Mockup Container - Fill entire space like PixiPreviewWrapper */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: viewMode === 'desktop'
            ? 'radial-gradient(ellipse at center, #1a1a2e 0%, #16213e 50%, #0f1419 100%)'
            : 'linear-gradient(135deg, #000000 0%, #1a1a1a 25%, #0d0d0d 50%, #1a1a1a 75%, #000000 100%)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <div style={{
            // Fill the entire available space like PixiPreviewWrapper
            width: viewMode === 'desktop' ? '100%' : viewMode === 'mobile-landscape' ? '700px' : '450px',
            height: viewMode === 'desktop' ? '100%' : viewMode === 'mobile-landscape' ? '400px' : '80%',
            maxWidth: 'none',
            maxHeight: 'none'
          }}>
            {/* Loading Screen Container */}
            <div
              ref={pixiContainerRef}
              className="w-full h-full overflow-hidden"
              style={{
                borderRadius: viewMode !== 'desktop' ? '28px' : '0px',
                border: viewMode !== 'desktop' ? '3px solid #333' : 'none',
                background: viewMode !== 'desktop'
                  ? 'linear-gradient(145deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%)'
                  : 'transparent',
                boxShadow: viewMode !== 'desktop'
                  ? '0 20px 40px -10px rgba(0, 0, 0, 0.8), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                  : 'none'
              }}
            />
          </div>
        </div>
      </div>

      {/* Asset Loading Status - Only show when loading */}
      {isLoading && (
        <div className="px-4 py-2 bg-gray-50 border-t border-gray-200 rounded-b-lg">
          <div className="grid grid-cols-2 gap-2 text-xs">
            {assetCategories.map((category) => (
              <div key={category.name} className="flex items-center justify-between">
                <span className="text-gray-600">{category.name}</span>
                <span className={`font-medium ${
                  category.status === 'complete' ? 'text-green-600' :
                  category.status === 'loading' ? 'text-blue-600' : 'text-gray-400'
                }`}>
                  {category.loaded}/{category.total}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfessionalLoadingPreview;